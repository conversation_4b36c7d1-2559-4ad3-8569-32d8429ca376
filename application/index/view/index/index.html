{include file="public/header" /}
<title>{$global.index_title} - {$global.web_name}</title>
{load href="/static/css/H/animate.css" /}
{load href="/static/js/H/jquery.SuperSlide.2.1.1.js" /}

<script type="text/javascript" src="https://hq.sinajs.cn/rn=1461855885572&list=s_sh000001,sh000001,s_sz399001,s_sz399001,sz399001"></script>
<script src="https://hq.sinajs.cn/rn=1528781848576&format=text&list=sinaindustry_up"></script>
<script src="https://hq.sinajs.cn/rn=1528781824076&format=text&list=sinaindustry_down"></script>
<script src="/static/js/H/template.js"></script>
<script src="/static/js/H/bianbianbian.js"></script>


<div class="banner">
    <div class="banner-slide">
        <div class="hd">
            <ul class="clearfix">
                {volist name="banner" id="vo"}
                <li>&nbsp;</li>
                {/volist}
            </ul>
        </div>
        <div class="bd">
            <ul>
                {volist name="banner" id="vo"}
                <li>
                    <a href="{$vo.link}" style="background: url({$vo.image}) no-repeat center top;"></a>
                </li>
                {/volist}
            </ul>
        </div>
        
    </div>

    <div class="wrapper clearfix" style="position: relative;z-index: 0;">          
        <div class="float-box">
            <div class="text">您炒股，我出钱</div>
            <div class="text-large">目前免息操盘20天</div>
            <div class="text" style="color:#8b8787;">资金双重保障，100%实盘交易</div>

            {if condition="$uid GT 0"}
            <div class="operate">
                <a href="{:url('members/index')}" class="btn btn-primary btn-block">个人中心</a>
            </div>
            <div class="extra-text">股市有风险，投资需谨慎</div>
            {else /}
            <div class="operate">
                <a href="{:url('Common/register')}" class="btn btn-primary btn-block">注册领红包</a>
            </div>
            <div class="extra-text" style="text-align: right;">已有账号？<a href="{:url('Common/login')}">立即登录</a></div>
            {/if}
        </div>
    </div>
</div>

<div class="notice">
    <div class="wrapper noticei"> 
        <i></i>
        <div class="notice-tit icon-index news">官方公告 | </div>
        <div class="notice-left">
            <div class="bd">
                <div class="tempWrap" >
                    <ul class="infoList" >
                        {volist name="noticeList" id="vo"}
                            <li><a href="{$vo.article_url}" target="_blank">{:cnsubstr($vo.title, 25)}</a><span>{$vo.add_time|date="Y-m-d",###}</span></li>
                        {/volist}   
                    </ul>
                </div>
            </div>
        </div>
        <div class="notice-more fr"><a href="/cms/wzgg.html" target="_blank">更多>></a></div>
    </div>
</div>

<div class="index_white_bg">
    <div class="wrapper clearfix">
        <ul class="pb01">
            <li>
                <div class="fleft"><i class="dw_Cbg"></i></div>
                <div class="fright bian-title">
                    <strong><span>累积配资人数</span></strong>
                    <p style="display: inline-block;">
                        <span class="timer count-title" id="count-number1" data-to="{$global.renshu}" data-speed="1500">{$global.renshu}</span>
                        <span id="i-two">人</span>
                    </p>
                </div>
            </li>
            <li>
                <div class="fleft"><i class="dw_Cbg"></i></div>
                <div class="fright bian-title">
                    <strong><span>累积配资金额</span></strong>
                    <p style="display: inline-block;">
                        <span class="timer count-title" id="count-number2" data-to="{$global.jine}" data-speed="1500">{$global.jine}</span>
                        <span id="i-two">元</span>
                    </p>
                </div>
            </li>
            <li>
                <div class="fleft"><i class="dw_Cbg"></i></div>
                <div class="fright bian-title">
                    <strong><span>累积利润赚取</span></strong>
                    <p style="display: inline-block;">
                        <span class="timer count-title" id="count-number3" data-to="{$global.lirun}"
                              data-speed="1500">{$global.lirun}</span>
                        <span id="i-two">元</span>
                    </p>
                </div>

            </li>
            <li>
                <div class="fleft"><i class="dw_Cbg"></i></div>
                <div class="fright bian-title">
                    <strong><span>按天配资余额</span></strong>
                    <p style="display: inline-block;">
                        <span class="timer count-title" id="count-number4" data-to="{$global.day}" data-speed="1500">{$global.day}</span>
                        <span id="i-two">元</span>
                    </p>
                </div>
            </li> 
            <li>
                <div class="fleft"><i class="dw_Cbg"></i></div>
                <div class="fright bian-title">
                    <strong><span>按月配资余额</span></strong>
                    <p style="display: inline-block;">
                        <span class="timer count-title" id="count-number5" data-to="{$global.month}" data-speed="1500">{$global.month}</span>
                        <span id="i-two">元</span>
                    </p>
                </div>
            </li>
        </ul>
    </div>

     <div class="newbee">
        <div class="wrapper clearfix">
            <div class="index-tezheng">
                <div class="partOne page-component">
                    <div class="wrap wrapper">
                        <div class="list">
                            <div class="partItem short">
                                <span class="icon"></span>
                                <p>灵活便捷</p>
                                <p class="little">闪电提现，十分钟到账</p>
                            </div>
                            <div class="partItem safe">
                                <span class="icon"></span>
                                <p>超低费用</p>
                                <p class="little">最低100元起配资，最高十倍的杠杆</p>
                            </div>
                            <div class="partItem moreLine">
                                <span class="icon"></span>
                                <p>真实安全</p>
                                <p class="little">成交数据资金第三方监管</p>
                            </div>
                            <div class="partItem deal">
                                <span class="icon"></span>
                                <p>交易保障</p>
                                <p class="little">采用大智慧专业股票交易</p>
                            </div>
                            <div class="partItem zyfu">
                                <span class="icon"></span>
                                <p>专业服务</p>
                                <p class="little">专业团队执业经验，有问必答</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<div class="wrapper plan">
    <div class=" plan-right">
        <div class="plan-right-list">
            <div class="project wow flipInY animated animated" data-wow-delay=".2s" style="visibility: visible; animation-delay: 0.2s; animation-name: flipInY;">
                <div class="in-icon02">免息配资</div>
                <div class="project-intro"> <span class="fs40 bule">全免</span>
                    <p class="fs14 tl40">不收取管理费，盈利的{$free_set.2}%归您</p>
                    <div class="line-x"></div>
                    <p class="fs14 t-l tl40">配资期限</span> <span class="right"><span class="bule">{$free_set.1}天</span></span>
                    </p>
                    <p class="fs14 t-l tl40">起配金额<span class="right"><span class="bule">{$free_money_range.0}元</span></span>
                    </p>
                    <div class=" clear mb30"></div>
                    <a rel="nofollow" href="{:url('stock/free')}" class="right-btn-b">立即申请</a>
                </div>
            </div>
        </div>
        
        <div class="plan-right-list">
            <div class="project wow flipInY animated animated" data-wow-delay=".2s" style="visibility: visible; animation-delay: 0.2s; animation-name: flipInY;">
                <div class="in-icon01">按天配资</div>
                <div class="project-intro"><span class="fs40 chengse">0.06%起费率</span>
                    <p class="fs14 tl40">适合放短线，短期操盘</p>
                    <div class="line-x"></div>
                    <p class="fs14 t-l tl40">操盘时间</span> <span class="right"><span class="chengse">{$day_use_time.0}-{:end($day_use_time)}天</span></span>
                    </p>
                    <p class="fs14 t-l tl40">最高可操盘 <span class="right"><span class="chengse">{:getMoneyFormt($day_money_range.1)}</span></span>
                    </p>
                    <div class=" clear mb30"></div>
                    <a rel="nofollow" href="{:url('stock/day')}" class="right-btn-r">立即申请</a>
                </div>
            </div>
        </div>

        <div class="plan-right-list">
            <div class="project wow flipInY animated animated" data-wow-delay=".2s" style="visibility: visible; animation-delay: 0.2s; animation-name: flipInY;">
                <div class="in-icon03">按月配资</div>
                <div class="project-intro"><span class="fs40 yello">{$month_max_multiple}倍最大杠杆比例</span>
                    <p class="fs14 tl40">操盘更灵活，利息更划算</p>
                    <div class="line-x"></div>
                    <p class="fs14 t-l tl40">操盘时间</span> <span class="right"><span class="yello">{$month_use_time.0}-{:end($month_use_time)}个月</span></span>
                    </p>
                    <p class="fs14 t-l tl40">最高可操盘 <span class="right"><span class="yello">{:getMoneyFormt($month_money_range.1)} </span></span>
                    </p>
                    <div class=" clear mb30"></div>
                    <a rel="nofollow" href="{:url('stock/month')}" class="right-btn-y ">立即申请</a>
                </div>
            </div>
        </div>
    </div>
</div>





<div class="body-container clearfix mt25" > 
    <div class="wrapper">
        <!--行情中心-->
        <div class="pb4">
            <div class="index_mod4">
                <div class="mod_right" >
                    <div class="section-left-bg"></div>
                    <h3 class="right-tit">行情中心</h3>
                    <div class="s_menu" id="menu_szzs">
                        <ul>
                            <li sv="0" class="current"><a>上证指数</a></li>
                            <li sv="1"><a>深证指数</a></li>
                        </ul>
                    </div>
                </div>
                <div class="mod_left">
                    <div class="hq_con">
                        <div class="hq_txt" id="a0001_bnt">
                            <a class="cur" sv="0" href="javascript:;">分时线</a> <a sv="1" href="javascript:;">日K线</a> <a sv="2" href="javascript:;">周K线</a> <a sv="3" href="javascript:;">月K线</a>
                        </div>
                        <div class="hq_img">
                            <img src="https://image.sinajs.cn/newchart/min/n/sh000001.gif" width="550" height="300" id="a0001_img">
                        </div>
                    </div>
                    <div class="hq_con" style="display:none;">
                        <div class="hq_txt" id="s0001_bnt">
                            <a class="cur" sv="0" href="javascript:;">分时线</a> <a sv="1" href="javascript:;">日K线</a> <a sv="2" href="javascript:;">周K线</a> <a sv="3" href="javascript:;">月K线</a>
                        </div>
                        <div class="hq_img">
                            <img src="https://image.sinajs.cn/newchart/min/n/sz399001.gif" width="550" height="300" id="s0001_img">
                        </div>
                    </div>
                    <div class="hq_sv">
                        <div class="hq_st">
                            <div class="hq_a1" id="a0001_v1">
                                <li class="sv">--</li>
                                <li class="ico"></li>
                                <li class="icon-right">--</li>
                                <li class="icon-right">--</li>
                            </div>
                            <div class="hq_aq1_xq" id="a0001_detail">
                                <p>
                                    <span>
                                        <a class="xq-color">今开：</a>
                                        <font></font>
                                    </span>
                                    <span>
                                        <a class="xq-color">最高：</a>
                                        <font></font>
                                    </span>

                                    <span>
                                        <a class="xq-color">昨收：</a>
                                        <font></font>
                                    </span>
                                    <span>
                                        <a class="xq-color">最低：</a>
                                        <font></font>
                                    </span>

                                    <span>
                                        <a class="xq-color">成交量：</a>
                                        <font></font>
                                    </span>
                                    <span>
                                        <a class="xq-color">振幅：</a>
                                        <font></font>
                                    </span>
                                    
                                    <span>
                                        <a class="xq-color" >成交额：</a>
                                        <font></font>
                                    </span>
                                </p>
                            </div>
                            <div class="gupiao">
                                <table width="50%">
                                    <thead>
                                    <tr>
                                        <td colspan="2"> 领涨板块 </td>
                                    </tr>
                                    </thead>
                                    <tbody id="gpBankuaiUp"></tbody>
                                </table>
                                <table width="50%">
                                    <thead>
                                    <tr>
                                        <td colspan="2"> 领跌板块 </td>
                                    </tr>
                                    </thead>
                                    <tbody id="gpBankuaiDown"></tbody>
                                </table>
                                <script id="bankuai" type="text/html">
                                {literal}
                                    {{each lists as item}}
                                    <tr>
                                        <td>
                                            {{item.name}}
                                        </td>
                                        {{if item.value > 0 }}
                                        <td style="color:#ff3646">
                                            {{item.value}}%
                                        </td>
                                        {{else if item.value < 0}}
                                        <td style="color:#237c02">
                                            {{item.value}}%
                                        </td>
                                        {{else}}
                                        <td>
                                            {{item.value}}%
                                        </td>
                                        {{/if}}
                                    </tr>
                                   {{/each}}
                                {/literal}
                                </script>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--行情中心 end-->
    </div>
</div>

<div class="body-container clearfix mt25" >
    <div class="wrapper plan3" style="background: #fff;">
        <div class="plan-right" style="width:100%;">            
            <div class="pr-left pr-jrzc" style="">
                <div class="prl-block bd">
                    <div class="prl-title">
                        <p>今日注册</p>
                    </div>
                    <div class="prl-li tempWrap">
                        <ul>
                            
                            {volist name="extend_data.jrcz" id="vo"} 
                                <li>
                                    <span class='li-l'>{$vo.field1}</span>
                                    <span class='li-r'>今日已注册成功</span>
                                    <p style='clear:both;'></p>
                                </li>
                            {/volist}
                        </ul>
                    </div>
                </div>
            </div> 
            <div class="app-d">
                <div class="jxrjxz">
                    <p>交易软件下载</p>
                </div>
                <div class="jxrjp">
                    <p>新用户注册并实名认证后请下载安装此交易软件</p>
                </div>
                <div class="jxrjlogo">
                    <img src="/static/img/H/extend/index_logo.jpg" alt="">
                </div>
                <div class="jxrjpc">
                    <button onclick="window.location='{$global.pc_jyapp}'">电脑版下载</button>
                </div>
            </div>
            <div class="pr-left pr-jrcz"  style="float:right;margin-right: 10px;">
                <div class="prl-block bd">
                    <div class="prl-title">
                        <p>今日充值</p>
                    </div>
                    <div class="prl-li tempWrap">
                        <ul>
                            {volist name="extend_data.jrcz" id="vo"} 
                            <li>
                                <span class='li-l'>{$vo.field1}</span>
                                <span class='li-r'>{$vo.field2}</span>
                                <p style='clear:both;'></p>
                            </li>                       
                            {/volist}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div style="clear:both;"></div>
    </div>
</div>


<div class="body-container wrapper pb6 plan4 clearfix mt25" >
    <div class="pb6-r fl">
        <div class="gonggaoBox">
            <div id="hd_two" class="dd">
                <p class="dd_line">
                    <span class="active" onclick="ggxwCheck(this,'noticeList2')">股市行情</span>
                    <span onclick="ggxwCheck(this,'hyList2')">配资常识</span>
                    <a href="/cms/gshq.html" target="_blank" class="gg" id='gdgg'>更多...</a>
                </p>
                <!-- 股市行情 -->
                <span id='noticeList2'>
                        {volist name="gshqList" id="vo"}
                        <li>
                            <span class="date">{$vo.add_time|date="Y-m-d",###}</span>
                            <a id="bd_colors" href="{$vo.article_url}" title="{:cnsubstr($vo.title,16)}" target="_blank">{:cnsubstr($vo.title,16)}</a>
                        </li>  
                        {/volist}
                </span>
                <!-- 配资常识 -->
                <span id='hyList2' style='display:none;'> 
                    {volist name="pzbkList" id="vo"}
                    <li>
                        <span class="date">{$vo.add_time|date="Y-m-d",###}</span>
                        <a id="bd_colors" href="{$vo.article_url}" title="{:cnsubstr($vo.title,16)}" target="_blank">{:cnsubstr($vo.title,16)}</a>
                    </li>  
                    {/volist}
                </span>
            </div>
        </div>
        <div style="clear: both;"></div>
    </div>
<script type="text/javascript">
    function ggxwCheck(d,t){
        // console.log(d,t);
        $(d).addClass('active').siblings('span').removeClass('active');
        // $('#noticeList2').toggle();
        // $('#hyList2').toggle();
        if(t=='noticeList2'){
            $('#noticeList2').fadeIn('slow');
            $('#hyList2').hide();
            $('#gdgg').attr('href','/cms/gshq.html');
        }else if(t=='hyList2'){
            $('#hyList2').fadeIn('slow');
            $('#noticeList2').hide();
            $('#gdgg').attr('href','/cms/pzbk.html');
        }
    }
</script> 
    <div class="down_load">
        <div class="load_top">
            <span>新用户领10000元实盘资金</span>
            <p>新用户注册并实名认证后  <br>
                立即领取10000元实盘资金<br>&nbsp;</p>
            <i></i>
            <a href="/download" target="_blank">马上下载</a>
        </div>
    </div>
    <div class="down_load index-tuigg">
        <div class="load_top">
            <span>注册送3999元大礼包</span>
            <p>注册会员后，完成任务赚现金<br>
                赠送的3999元只能用于支付管理费用<br>&nbsp; </p>
            <i></i>
            <a href="{:url('Common/register')}" target="_blank">马上注册</a>
        </div>
    </div>
</div>
<!-- over-->



<div class="body-container pb6-bg  yqlj">
    <div class="pb7  clearfix wrapper" style="height: auto;margin-bottom: 0;"> 
        <div class="pb7-bottom">
            <div class="floor-hd">
                <!-- <i class="index_link_icon icon-hyzx"></i> -->
                <h3>合作伙伴</h3>
            </div>
            <div class="linkM">
                <div class="linkPic" style="">
                    {volist name="friend" id="vo"}
                        <a href="{$vo.link_href}" target="_blank">
                            <img  src="{$vo.link_img}" alt="">
                        </a>
                    {/volist}
                </div>
            </div>
        </div>
    </div>
</div>




<!-- 首页弹窗图片模式 -->
{if condition="$global.web_show EQ 2"}
<div class="chz_indexs">
    <div class="modal-mask"></div>
    <div class="modalmain">
        <div class="close" onclick='t()'><img src="/static/img/H/extend/closed.png"></div>
        <div class="modal-cont">
            {:get_ad(16)}
        </div>
    </div>
</div>
{/if}
<!-- 首页弹窗文字模式 -->
{if condition="$global.web_show EQ 1"}
<div class="txt_indexs">
    <div class="modal-mask"></div>
    <div class="modalmain">
        <div class="modal-title">网站公告</div>
        <div class="modal-cont">
            {:get_ad(16)}
        </div>
        <div class="modal-bottom" onclick='t()'>
            我知道了
        </div>
    </div>
</div>

{/if}

<div id="daiyanbao_com_content" closerate="1" playrate="-2" style="position: fixed;_position: absolute;text-align: left;overflow: visible;bottom :0;left:0;display:block; z-index:999;">
            <script src="https://res.daiyanbao.com/client/vip/9bb281a8-5cb0-563f-8ad4-3cc7e0fa1e6f.js"></script>
</div>


{load href="/static/js/H/Marquee.js" /}

<script type="text/javascript">
function t(){
    $('.chz_indexs,.txt_indexs').hide(288);
}
$(".banner-slide").slide({
    mainCell: ".bd ul",
    autoPlay: true,
    interTime: 5000
});
$('#marquee1').kxbdSuperMarquee({
    distance: 225,
    time: 3,
    btnGo:{left:'#goR',right:'#goL'},
    direction:'left'
});
jQuery(".notice-left").slide({
    titCell: ".hd ul",
    mainCell: ".bd ul",
    autoPage: true,
    effect: "top",
    autoPlay: true,
    vis: 1
}); 
jQuery(".pr-left").slide({
        titCell: ".hd ul",
        mainCell: ".bd ul",
        autoPage: true,
        effect: "top",
        autoPlay: true,
        vis: 8
    });
</script>

<script type="text/javascript">
function hq_code(s) {
    var zqcode = "sh000001";
    if (s == '0') $("#a0001_img").attr('src', 'https://image.sinajs.cn/newchart/min/n/' + zqcode + '.gif');
    if (s == '1') $("#a0001_img").attr('src', 'https://image.sinajs.cn/newchart/daily/n/' + zqcode + '.gif');
    if (s == '2') $("#a0001_img").attr('src', 'https://image.sinajs.cn/newchart/weekly/n/' + zqcode + '.gif');
    if (s == '3') $("#a0001_img").attr('src', 'https://image.sinajs.cn/newchart/monthly/n/' + zqcode + '.gif');
}
function hq_code1(s) {
    var zqcode = "sz399001";
    if (s == '0') $("#s0001_img").attr('src', 'https://image.sinajs.cn/newchart/min/n/' + zqcode + '.gif');
    if (s == '1') $("#s0001_img").attr('src', 'https://image.sinajs.cn/newchart/daily/n/' + zqcode + '.gif');
    if (s == '2') $("#s0001_img").attr('src', 'https://image.sinajs.cn/newchart/weekly/n/' + zqcode + '.gif');
    if (s == '3') $("#s0001_img").attr('src', 'https://image.sinajs.cn/newchart/monthly/n/' + zqcode + '.gif');
}
$("#a0001_bnt a").click(function() {
    hq_code($(this).attr('sv'));
    $("#a0001_bnt a").removeClass('cur');
    $(this).addClass('cur');
})
$("#s0001_bnt a").click(function() {
    hq_code1($(this).attr('sv'));
    $("#s0001_bnt a").removeClass('cur');
    $(this).addClass('cur');
})
$("#menu_szzs li").click(function() {
        $("#menu_szzs li").removeClass("current");
        $(this).addClass("current");
        $(".hq_con").hide();
        $(".hq_con").eq($(this).attr("sv")).show();
        if ($(this).attr("sv") == '0') {
            hq_show(0);
        } else {
            hq_show(1);
        }
    })
    //当前股票价
function hq_show(type) {
    if (type == 0) {
        var elements = hq_str_sh000001.split(",");
        var elements_bl = hq_str_s_sh000001.split(",");
    } else {
        var elements = hq_str_sz399001.split(",");
        var elements_bl = hq_str_s_sz399001.split(",");
    }

    var a0001_k_pr = parseFloat(elements[1]);
    var a0001_t_pr = parseFloat(elements[3]);

    $("#a0001_v1 li").eq(0).text(a0001_t_pr.toFixed(2));
    $("#a0001_v1 li").eq(2).text( parseFloat(elements_bl[2]) > 0 ? "+" + parseFloat(elements_bl[2]).toFixed(2) :parseFloat(elements_bl[2]).toFixed(2) )
    $("#a0001_v1 li").eq(3).text(  parseFloat(elements_bl[2]) > 0 ? "+" + parseFloat(elements_bl[3]).toFixed(2) : parseFloat(elements_bl[3]).toFixed(2))
    $("#a0001_v1 li").eq(3).text($("#a0001_v1 li").eq(3).text() + '%');

    $("#a0001_detail font").eq(0).text(a0001_k_pr.toFixed(2));//今开
    // $("#a0001_detail font").eq(1).text( (pr_style(parseFloat(elements_bl[4]) * 100)) + '手');
    $("#a0001_detail font").eq(1).text(parseFloat(elements[4]).toFixed(2));//最高
    $("#a0001_detail font").eq(2).text(parseFloat(elements[2]).toFixed(2));//昨收
    $("#a0001_detail font").eq(3).text(parseFloat(elements[5]).toFixed(2));//最低

    if( type==0 ){
        $("#a0001_detail font").eq(4).text( (parseFloat(elements_bl[4]) / 100).toFixed(2) + '万手');//成交量
    }else{
        $("#a0001_detail font").eq(4).text( (parseFloat(elements_bl[4]) / 10000).toFixed(2) + '万手');//成交量
    }

    $("#a0001_detail font").eq(5).text( ( (parseFloat(elements[4]) - parseFloat(elements[5]) )/ elements[2] * 100 ).toFixed(2) + "%");//振幅
    $("#a0001_detail font").eq(6).text(pr_style(parseFloat(elements_bl[5]) * 10000));//成交额

    // $("#a0001_detail font").eq(7).text(pr_style((parseFloat(elements_bl[4]) * 100) * parseFloat(a0001_k_pr.toFixed(2))));

    if (parseFloat(elements_bl[2]) < 0) {
        $("#a0001_v1").removeClass("up").addClass("down");
        $(".hq_aq1_xq").removeClass("up").addClass("down");
        $("#a0001_v1 li").eq(0).css("color", "#009900");
        $("#a0001_v1 li").eq(1).removeClass('ico').addClass('dw');
    }

    if (parseFloat(elements_bl[2]) > 0){
        $(".hq_aq1_xq").removeClass("down").addClass("up");
        $("#a0001_v1").removeClass("down").addClass("up");

        $("#a0001_v1 li").eq(0).css("color", "#FF0000");
        $("#a0001_v1 li").eq(1).removeClass('dw').addClass('ico');
    }
}
hq_show(0);
function pr_style(s) {
    var re_pr = 0;
    var re_str = "";
    if (s > 10000 && s < ********) {
        re_pr = s / 10000;
        re_str = '万';
    } else if (s > ******** && s < *********) {
        re_pr = s / ********;
        re_str = '千万';
    } else if (s >= *********) {
        re_pr = s / *********;
        re_str = '亿';
    } else if (s < 10000) {
        re_pr = s;
    }
    if (s < 10000) {
        re_pr = re_pr;
    } else {
        re_pr = re_pr.toFixed(2);
    }
    return (re_pr + re_str);
}
function fetchBanKuaiData (variableName, hookElement) {
    // 板块领涨领跌
    var bankuai = variableName.slice(0, 4);
    var bankuaiArr = [];
    for(var i = 0 ; i < bankuai.length; i++){
        var tempArr= bankuai[i].split(',')
        bankuaiArr[i] = {};
        bankuaiArr[i].name = tempArr[1];
        bankuaiArr[i].value = tempArr[5];
    }
    hookElement.html(template('bankuai', {lists: bankuaiArr}));
}
fetchBanKuaiData(sinaindustry_up, $('#gpBankuaiUp'));
fetchBanKuaiData(sinaindustry_down, $('#gpBankuaiDown'));
</script>

{load href="/static/js/H/loginregister.js" /}
<script type="text/javascript">
$('.loginreg .login_reg_title a').each(function(index, el) {
    $(this).on('click', function(event) {
        $(this).addClass('on').siblings('a').removeClass('on');
        $('.loginreg .form .loginreg-block').eq(index).fadeIn(300).siblings('.loginreg-block').hide();
    });
});
$('#goreg').on('click', function(event) {
    $('.loginreg .login_reg_title a').eq(0).addClass('on').siblings('a').removeClass('on');
    $('.loginreg .form .loginreg-block').eq(0).fadeIn(300).siblings('.loginreg-block').hide();
});

var loginUrl = '{:url("Common/login")}';
var smsUrl = '{:url("Common/user_verify")}';
var regUrl = '{:url("Common/register")}';
var member_index = '{:url("members/index")}';
</script>
{include file="public/footer" /}