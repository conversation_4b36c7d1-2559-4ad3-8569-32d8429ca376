:root {
    --primary-color: #3B7AFE;
}

.head_text {
    /*padding-top:5px;*/
    padding: 5px 5px 0 5px;
    font-size: 12px;
    line-height: 20px;
}

.wap_photo {
    height: 160px;
    background: #fff;
    box-shadow: 0 0px 1px rgba(0, 0, 0, .1);
    padding: 0 32px;
    position: relative;
    background: linear-gradient(180.67deg, #6AB2FE 0.57%, #3977FE 99.43%);
}

.wap_photo .img_photo {
    width: 80px;
    height: 80px;
    float: left;
    overflow: hidden;
    margin: 13px 13px 0 13px;
}

.wap_photo .img_photo img {
    border-radius: 160px;
}

.qiandao {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 50px;
    line-height: 20px;
    text-align: center;
    font-size: 12px;
    border-radius: 4px;
    background: #E5EDFF;
    color: var(--primary-color);
}

.height10 {
    height: 10px;
}

.btn_cz {
    width: 40%;
    height: 30px;
    line-height: 30px;
    display: inline-block;
    border-radius: 4px;
    background: #fff;
    color: var(--primary-color);
    text-align: center;
    font-size: 14px;
    margin: 0px 3%;
    margin-top: 30px
}

.zc_table {
    color: #fff;
    font-size: 12px;
    line-height: 20px;
    margin-top: 5px;
    width: 100%;
    text-align: left
}

.zc_table td {
    width: 33.33%
}

.user_box {
    width: 100%
}

.user_box ul {
    width: 100%
}

.user_box ul li {
    position: relative;
    height: 40px;
    border-bottom: 1px solid #eee;
    background: #fff
}

.user_box ul li:last-child {
    /*border-bottom:0;*/
}

.user_box ul .icon {
    position: absolute;
    width: 40px;
    height: 40px;
    left: 0;
    top: 0;
    text-align: center;
}

.user_box ul .icon img {
    width: 16px;
    margin-top: 12px
}

.user_box ul .title {
    padding-left: 40px;
    line-height: 40px;
    color: #666;
    font-size: 14px
}

.user_box ul .tips {
    position: absolute;
    width: 140px;
    height: 40px;
    line-height: 40px;
    right: 30px;
    top: 0;
    color: #ccc;
    text-align: right;
    font-size: 12px
}

.user_box ul .tips span {
    color: red;
    padding-right: 5px
}

.user_box ul .more {
    position: absolute;
    width: 20px;
    height: 40px;
    line-height: 40px;
    right: 8px;
    top: 0;
    background-image: url(../img/right.png);
    background-repeat: no-repeat;
    background-position: center;
    background-size: 8px
}

.user_box ul .tishi {
    min-width: 14px;
    text-align: center;
    line-height: 14px;
    display: inline-block;
    position: absolute;
    right: 26px;
    top: 7px;
    background: red;
    color: #fff;
    border-radius: 17px;
    padding: 6px 8px;
    font-size: 16px;
    transform: scale(.7);
    font-family: Tahoma !important;
}

.tuichu {
    text-align: center;
    line-height: 40px;
    color: #333;
    font-size: 16px;
    background: var(--primary-color);
}

.account-money-title {
    text-align: center;
    font-size: 16px;
    padding-top: 20px
}

.account-money {
    text-align: center;
    font-size: 30px;
    padding-top: 10px;
}




.btn_primary {
    display: block;
    width: 100%;
    height: 40px;
    line-height: 40px;
    text-align: center;
    color: #fff;
    font-size: 16px;
    background: var(--primary-color);
    border-radius: 3px;
    margin: 0 auto;
    border: none;
}




.formbox {
    background: #fff;
    margin: 5%;
}

.formbox table {
    width: 100%;
}

.formbox tr.bgc1 td {
    background: url(../img/bg72.png) no-repeat 20% 50%;
}

.formbox tr.bgc1 th,
.formbox tr.bgc1 td {
    background-color: #f5f5f5;
}

.formbox th {
    width: 90px;
    line-height: 24px;
    padding: 5px;
    border: 1px solid #e8e8e8;
    color: #666;
    text-align: center;
    font-size: 14px;
}

.formbox td.r {
    text-align: right;
    padding-right: 10px;
}

.formbox td {
    line-height: 35px;
    padding: 5px 0 5px 5px;
    border: 1px solid #e8e8e8;
    font-size: 14px;
}

.formbox tr.bgc1 td strong {
    font-size: 15px;
    color: var(--primary-color);
}

.formbox strong {
    margin-right: 5px;
}

.formbox td.r-e {
    background: #fff url(../img/bg71.png) 20% 50% no-repeat;
}

.formbox td.r-p {
    background: #fff url(../img/bg71.png) 20% 50% no-repeat;
}

.formbox td.r-p,
.formbox td.r-m,
.formbox td.r-e {
    text-align: right;
    padding: 5px 10px;
    line-height: 24px;
    font-size: 14px;
}

.formbox td.r-p h6,
.formbox td.r-m h6,
.formbox td.r-e h6 {
    font-weight: 300;
    color: #666;
}

.formbox span {
    margin-right: 5px;
}



/*message*/
.user_box ul .title.pl15 {
    padding-left: 12px;
}

.user_box ul .dian {
    width: 8px;
    height: 8px;
    border-radius: 100%;
    background: red;
    display: -webkit-inline-box;
    margin-right: 8px;
}

.user_box ul .time {
    position: absolute;
    width: 100px;
    height: 40px;
    line-height: 40px;
    right: 10px;
    top: 0;
    color: #999;
    text-align: right;
}

.dian2 {
    width: 8px;
    height: 8px;
    border-radius: 100%;
    background: #CCC !important;
    display: -webkit-inline-box;
    margin-right: 10px;
}

.user_box_bg {
    background: rgba(0, 0, 0, .7);
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 998;
}

.user_box_bg_con {
    background: rgba(255, 255, 255, .9);
    position: fixed;
    width: 80%;
    margin: auto;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    height: auto;
    display: inline-table;
    overflow: hidden;
    z-index: 99999;
    -webkit-transition: -webkit-transform .3s;
    transition: -webkit-transform .3s;
    transition: transform .3s;
    transition: transform .3s, -webkit-transform .3s;
}

.us_text {
    width: 100%;
    padding: 6%;
    color: #666;
    font-size: 14px;
}

.us_col {
    width: 100%;
    text-align: center;
    font-size: 16px;
    color: #333;
    border-top: 1px solid #ccc;
    height: 46px;
    line-height: 46px;
}

.red_d {
    min-width: 14px;
    text-align: center;
    line-height: 14px;
    display: inline-block;
    position: absolute;
    right: 21px;
    top: 0;
    background: red;
    color: #fff;
    border-radius: 17px;
    padding: 5px 7px;
    font-size: 14px;
    transform: scale(.7);
    font-family: Tahoma !important;
}

.yidu {
    width: 96%;
    text-align: center;
    height: 40px;
    line-height: 40px;
    background: #fff;
    font-size: 16px;
    color: var(--primary-color);
    margin: 2%;
    border-radius: 3px;
    border: 1px solid currentColor;
}

.text_con {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 70%;
}

.sqyanqi.stockpass table {
    margin: 20px 0 15px;
    border-collapse: collapse;
    width: 100%;
}

.sqyanqi.stockpass table td,
.sqyanqi table th {
    width: 120px;
    padding: 5px 10px;
    border: solid 1px #ddd;
    text-align: center;
    line-height: 27px;
    font-weight: normal;
}

.sqyanqi.stockpass td.app-down a {
    background: #FF3F00;
    padding: 5px 30px;
    color: #fff;
    margin-right: 10px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 14px
}









/*推广*/
.ms-c6-t {
    height: 35px;
    background: #f4f4f4;
}

.ms-c6-t ul li {
    float: left;
    line-height: 36px;
    padding: 0 5px;
    border-right: 1px solid #eceaea;
    width: 20%;
    text-align: center;
    font-size: 12px
}

.ms-c6-t ul li.current {
    background: #fff;
    border-top: 3px solid #fff;
    border-right: 1px solid #eee;
    border-left: 1px solid #eee;
    line-height: 30px;
    border-bottom: 3px solid #F30;
}

.ms-c6-t ul li a {
    color: #333
}

.ms-c6-t ul li a:hover {
    color: #F30;
}

.ms-c6-m {
    padding: 0 10px 20px;
}

.ms-c6-table table {
    border: 1px solid #fff;
    border-collapse: collapse;
    width: 100%;
}

.ms-c6-table th {
    line-height: 30px;
    background: #fff2df;
    font-weight: normal;
}

.ms-c6-table td {
    line-height: 30px;
    border-bottom: 1px solid #eee;
    padding: 10px 3px;
    text-align: center;
}

.ms-c6-table tr:nth-child(odd) {
    background: #f9f9f9;
}

.ms-c6-b {
    padding-top: 1px;
}

.ms-c6-b dl {
    padding: 10px 15px;
    background: #E5EDFF;
}

.ms-c6-b dt {
    line-height: 30px;
    font-family: "Microsoft YaHei", "微软雅黑";
    font-size: 16px;
    color: var(--primary-color);
}

.ms-c6-b dd {
    line-height: 30px;
    color: #666;
    font-size: 13px;
}

.ms-c6-ts p {
    color: #F00;
    line-height: 30px;
    padding-bottom: 20px;
}

.ms-c6-ts p strong {
    margin: 0 5px;
}

.promotion-ma {
    padding: 15px;
    color: var(--primary-color);
}

.promotion-center {
    text-align: center;
}

.promotion-center img {
    border: 2px solid #eee;
}

.promotion-link {
    padding: 0 15px 15px 15px;
    color: #666;
    word-wrap: break-word;
    line-height: 22px;
}

.copy-link {
    margin: 0 15px
}

.search {
    clear: both;
    padding: 5px 20px;
    font-size: 12px;
}

.search input[type='text'] {
    border: 1px solid #ccc;
    padding: 3px;
    width: 80px;
    font-size: 12px;
    height: 24px
}

.search input[type='submit'] {
    border: none;
    padding: 3px 10px;
    background: #fe825e;
    color: #fff;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px
}










/*realname*/
.real-div {
    padding: 10px;
    border: 1px solid red;
    background: #fff;
    margin: 5%;
    line-height: 25px
}

.pass-p {
    color: red;
    font-size: 14px
}

.pass-bank {
    color: #777;
    font-size: 12px
}

.blue-a {
    color: #069
}





/*bank*/
.card-box {
    height: 100px;
}

.card {
    border: 1px solid #dedede;
    border-radius: 4px;
    height: 80px;
    margin: 0 15px 9px;
    padding: 10px;
    position: relative;
    box-shadow: 0 11px 14px rgba(245, 61, 82, 0.07);
    background: #fff;
}

.card span {
    float: right;
    line-height: 38px;
    padding: 0px 10px;
    font-family: "Microsoft YaHei", "微软雅黑";
    font-size: 14px;
    color: var(--primary-color);
}

.card .bank-logo {
    width: auto;
    height: 30px;
    float: left;
    padding-top: 0px;
    font-size: 18px;
}

.card .bank-logo img {
    width: 150px;
    height: auto;
}

.card strong {
    font-size: 17px;
    color: #000;
    font-weight: 300;
}

.card .inpbox {}

.card .inpbox b {
    line-height: 24px;
    font-style: normal;
    float: left;
    margin-left: 10px;
}

.card .inpbox i {
    font-style: normal;
    float: left;
    margin-top: 6px;
    font-size: 18px;
}

.card .inp-c {
    width: 90px;
    border: 1px solid #ccc;
    padding: 3px;
    background: #fff;
    margin-right: 5px;
    float: left;
    height: 15px;
}

.card .btn-c {
    background: #F60;
    color: #fff;
    height: 22px;
    margin: 0 auto;
    font-size: 12px;
    vertical-align: top;
    padding: 0 10px;
}

.card .btn-c:hover {
    background: #F90;
}

.bank-p {
    padding: 10px 15px;
    color: #fff;
}







/*充值*/
.list li {
    display: block;
    position: relative;
    border-bottom: 1px solid #eee;
    background: #fff;
    padding: 10px;
    padding-left: 55px;
}

.list li:last-child {
    border: 0
}

.list li img {
    position: absolute;
    top: 14px;
    left: 15px;
    width: 30px;
    height: 30px;
}

.list li p {
    font-size: 14px;
}

.list li i {
    display: block;
    font-size: 10px;
    color: #999
}

.account {
    background: #fff;
    padding: 10px 15px;
}

.account li {
    font-size: 14px;
    line-height: 36px;
}

.account li .content {
    line-height: 22px;
}

.account li span {
    padding: 2px 10px;
    color: #0af;
    margin-left: 10px;
}

.list_k li {
    display: block;
    position: relative;
    border-bottom: 1px solid #eee;
    background: #fff;
    height: 46px;
    line-height: 46px;
    padding: 0 15px;
}

.list_k li:last-child {
    border: 0
}

.list_k li p {
    font-size: 14px;
    width: 25%;
    display: inline-block;
}

.list_k li input {
    display: inline-block;
    border: none;
    font-size: 14px;
    color: #333;
    width: 69%;
    height: 100%;
    padding: 0 5px;
}

.tishi {
    padding: 10px 15px;
    background: #F5F5F5;
}

.tishi p {
    color: #777
}

.tx_ts {
    font-size: 12px;
    color: #777;
    padding: 10px 15px
}

.btncopy {
    width: 44px;
    border: 0px;
    padding: 3px 0;
    margin-left: 4px;
}

.formbox.withdraw-css table td {
    text-align: left
}

.formbox .inp {
    border: 1px solid #eee;
    background: #fcfcfc;
    padding: 2px 5px;
    width: 120px;
    border-radius: 3px;
    font-size: 12px;
    margin-right: 5px;
}

.ms-c6-dl {
    min-height: 130px;
    padding: 10px 0;
}

.ms-c6-dl dl {
    width: 100%;
    padding-bottom: 5px;
}

.ms-c6-dl dl dt {
    padding: 5px 10px;
    font-size: 15px;
}

.ms-c6-dl dl dt strong {
    color: #F00;
    margin: 0 3px;
    font-size: 20px;
}

.ms-c6-dl dl dd {
    padding: 5px 10px;
    color: #666;
}

.ms-c6-dl p {
    clear: both;
    margin: 0 5%;
    line-height: 25px;
    color: red;
    padding-top: 5px;
    font-size: 14px
}








/*stocklist*/
.ms-c9 {
    font-size: 13px;
    margin-bottom: 15px;
    box-shadow: 0px 0px 10px 0px #e0d7d7;
    border-radius: 12px;
    overflow: hidden;
}

.ms-c9 .c9title {
    height: 34px;
    line-height: 34px;
    overflow: hidden;
    border-bottom: 0;
    color: #333;
    background: #f5f5f5;
    border: 1px solid #e8e8e8;
    border-bottom: 0;
}

.ms-c9 .c9title span.l {
    float: left;
    height: 34px;
    line-height: 34px;
    padding-left: 8px;
    /*background: url(../images/bg03.png) 8px 9px no-repeat;*/
}

.ms-c9 .c9title span.r {
    float: right;
    height: 34px;
    line-height: 34px;
    padding-right: 10px;
}

.ms-c9 table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #e8e8e8;
}

.ms-c9 table th {
    text-align: right;
    width: 100px;
    border: 1px solid #e8e8e8;
    background: #fcfcfc;
    font-weight: 300;
    line-height: 20px;
    padding: 5px;
    color: #666;
}

.ms-c9 table td {
    border: 1px solid #e8e8e8;
    line-height: 20px;
    padding: 6px 5px;
    color: #333;
}

.ms-c9 table td.bgred {
    background: #ca0c00;
    color: #fff;
}

.ms-c9 table td.bggreen {
    background: #0f990f;
    color: #fff;
}

.ms-c9 table .yellow {
    color: #C90
}

.ms-c9 table .red {
    color: #F60;
}

.ms-c9 table td.daishenhe {
    font-size: 12px;
    font-weight: bold;
    color: #199760;
}

.ms-c9 table td.shibai,
.ms-c9 table td.yijieshu {
    font-size: 12px;
    font-weight: bold;
    color: #999999;
}

.ms-c9 table td.caopanzhong {
    font-size: 12px;
    font-weight: bold;
    color: #D23636;
}

.ms-c9 .c9btn {
    height: 35px;
    padding-top: 8px;
}

.ms-c9 .c9btn a {
    border: 1px solid #ddd;
    font-family: arial, "Microsoft Yahei";
    font-size: 12px;
    display: inline-block;
    color: #67788c;
    margin-right: 5px;
    height: 26px;
    line-height: 26px;
    background: #e9e9e9 url(../images/bg25.png) repeat-x;
    cursor: pointer;
    border-radius: 0px;
    width: 90px;
    text-align: center
}

.ms-c9 .c9btn a:hover {
    color: #F60;
}

.ms-c9 .btn {
    display: inline-block;
    background-repeat: no-repeat;
    background-color: #f5f5f5;
    background-position: 2px center;
    background-size: 12px;
    font-size: 12px;
    color: #333;
    border: 1px solid #ccc;
    background-position: 10px;
    padding: 0 5px;
    line-height: 28px;
    margin: 0;
    border-radius: 6px;
    min-width: 66px;
    text-align: center;
    box-sizing: border-box;
    flex-shrink: 0;
}

/* 按鈕容器樣式 - 確保按鈕對齊 */
.ms-c9 table td[colspan="4"] {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: flex-start;
    padding: 5px 3px 8px 3px;
    line-height: 1.2;
    gap: 2px 1px;
}

/* 前往操盤按鈕樣式 - 保持與其他按鈕相同大小，只改變顏色 */
.ms-c9 .btn.btn-trading {
    background-color: #4285f4;
    color: #fff;
    border: 1px solid #4285f4;
}

/*.ms-c9 #btn_view{background-image: url('../img/r_view.png');}
.ms-c9 #btn_add{background-image: url('../img/r_add.png')}
.ms-c9 #btn_fillloss{background-image: url('../img/r_fillloss.png')}
.ms-c9 #btn_profit{background-image: url('../img/r_profit.png')}
.ms-c9 #btn_end{background-image: url('../img/r_end.png')}
.ms-c9 #btn_password{background-image: url('../img/r_password.png'); }*/
/*stocklist end*/






/*stock-deal*/
.stock-deal {
    font-size: 12px;
    padding: 15px 0;
    text-align: center;
}

.stock-deal a {
    text-decoration: none;
}

.stock-deal .box_prompt {
    background: #fffcef;
    border: 1px solid #ffbb76;
    color: var(--primary-color);
    font-size: 12px;
    line-height: 18px;
    padding: 5px 10px;
    text-align: left;
}

.stock-deal .box_word .inp {
    border: 1px solid #ccc;
    height: 30px;
    width: 50%;
    padding: 0 5px;
    border-radius: 4px;
}

.stock-deal .operate-group .btn {
    display: inline-block;
    padding: 0;
    width: 30%;
    font-size: 12px;
    line-height: 28px;
    border-radius: 5px;
    margin: 0 5px;
    text-align: center;
}

.stock-deal .operate-group .btn.btn-cancel {
    background-color: #c8c8c8;
    color: #ffffff;
}

.stock-deal .operate-group .btn.btn-primary {
    background-color: var(--primary-color);
    color: #ffffff;
}

.stock-deal table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    text-align: center;
}

.stock-deal .sqyanqi table {
    margin: 0 auto;
    border-left: solid 1px #ddd;
    border-bottom: solid 1px #ddd;
}

.stock-deal .sqyanqi table td,
.stock-deal .sqyanqi table th {
    width: 120px;
    padding: 5px 10px;
    border: solid 1px #ddd;
    text-align: center;
    line-height: 27px;
    font-size: 13px;
}

.stock-deal .box_word select {
    width: 35%;
    height: 25px;
    background: #fff;
    border-radius: 3px;
    padding: 0px 3px;
}

/*stock-deal*/